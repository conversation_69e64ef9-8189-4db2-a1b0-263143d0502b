document.addEventListener('DOMContentLoaded', function() {
    console.log('AutoShopee Data Scraper đã sẵn sàng');

    // <PERSON><PERSON><PERSON> phần tử DOM
    const step1 = document.getElementById('step1');
    const step2 = document.getElementById('step2');
    const shopIdsTextarea = document.getElementById('shop_ids');
    const outputPathInput = document.getElementById('output_path');
    const filenameInput = document.getElementById('filename');
    const processBtn = document.getElementById('process_btn');
    const pasteBtn = document.getElementById('paste_btn');
    const backBtn = document.getElementById('back_btn');
    const loginInfoBtn = document.getElementById('login_info_btn');
    const convertCsvBtn = document.getElementById('convert_csv_btn');
    const progressBar = document.getElementById('progress_bar');
    const timerLabel = document.getElementById('timer_label');
    const shopCounter = document.getElementById('shop_counter');
    const progressTable = document.getElementById('progress_table');
    const logContainer = document.getElementById('log_container');
    const totalProducts = document.getElementById('total_products');
    const browseBtn = document.getElementById('browse_btn');
    const fileResult = document.getElementById('file_result');
    const fileResultText = document.getElementById('file_result_text');
    const fileResultLink = document.getElementById('file_result_link');

    // Modal đăng nhập
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const saveLoginBtn = document.getElementById('save_login_btn');

    // Modal CSV
    const csvModal = new bootstrap.Modal(document.getElementById('csvModal'));
    const csvFileInput = document.getElementById('csv_file');
    const uploadCsvBtn = document.getElementById('upload_csv_btn');
    const csvProgress = document.getElementById('csv_progress');
    const csvResult = document.getElementById('csv_result');
    const downloadCsvResult = document.getElementById('download_csv_result');

    // Modal browse thư mục
    const folderBrowserModal = new bootstrap.Modal(document.getElementById('folderBrowserModal'));
    const currentPathInput = document.getElementById('current_path');
    const refreshFolderBtn = document.getElementById('refresh_folder_btn');
    const folderList = document.getElementById('folder_list');
    const folderLoading = document.getElementById('folder_loading');
    const selectFolderBtn = document.getElementById('select_folder_btn');
    const createFolderBtn = document.getElementById('create_folder_btn');

    // Raw Data Processing elements - sử dụng getElementById trong functions

    // Biến cục bộ
    let currentTaskId = null;
    let statusCheckInterval = null;
    let timerInterval = null;
    let startTime = null;
    let currentBrowsePath = '';

    // Raw Data Processing variables
    let rawDataSourceSpreadsheetId = '';
    let rawDataTargetSpreadsheetId = '';
    let rawDataSelectedSheets = [];

    // Internal Data variables
    let internalDataSourceId = '';
    let internalDataSheets = {};
    let internalDataSelectedSheets = [];
    let internalDataCurrentTaskId = null;
    let internalDataDealIds = [];

    // Lấy thông tin đăng nhập mặc định
    fetch('/api/get_default_credentials')
        .then(response => response.json())
        .then(data => {
            usernameInput.value = data.username;
            passwordInput.value = data.password;
        })
        .catch(error => console.error('Lỗi khi lấy thông tin đăng nhập:', error));

    // Đăng nhập tự động khi trang được tải
    fetch('/api/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})  // Sử dụng thông tin đăng nhập mặc định
    })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                showAlert('Đăng nhập thất bại: ' + data.message, 'danger');
                processBtn.disabled = true;
            } else {
                processBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Lỗi khi đăng nhập:', error);
            showAlert('Lỗi kết nối khi đăng nhập', 'danger');
            processBtn.disabled = true;
        });



    // Xử lý sự kiện nút Browse
    browseBtn.addEventListener('click', function() {
        // Mở modal browse thư mục
        currentBrowsePath = '';
        loadFolderList('');
        folderBrowserModal.show();
    });

    // Xử lý sự kiện nút Paste
    pasteBtn.addEventListener('click', function() {
        navigator.clipboard.readText()
            .then(text => {
                processAndFormatIds(text);
            })
            .catch(err => {
                console.error('Không thể truy cập clipboard:', err);
                showAlert('Không thể truy cập clipboard', 'danger');
            });
    });

    // Xử lý sự kiện nút Xử lý dữ liệu
    processBtn.addEventListener('click', function() {
        startProcessing();
    });

    // Xử lý sự kiện nút Quay lại
    backBtn.addEventListener('click', function() {
        stopStatusCheck();
        step2.style.display = 'none';
        step1.style.display = 'block';
        fileResult.style.display = 'none';
    });

    // Xử lý sự kiện nút Thông tin đăng nhập
    loginInfoBtn.addEventListener('click', function() {
        loginModal.show();
    });

    // Xử lý sự kiện nút Lưu thông tin đăng nhập
    saveLoginBtn.addEventListener('click', function() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        if (!username || !password) {
            showAlert('Vui lòng nhập đầy đủ thông tin đăng nhập', 'warning');
            return;
        }

        // Gọi API cập nhật thông tin đăng nhập
        fetch('/api/update_credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loginModal.hide();
                    showAlert('Đã cập nhật thông tin đăng nhập', 'success');
                } else {
                    showAlert('Lỗi: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Lỗi khi cập nhật thông tin đăng nhập:', error);
                showAlert('Lỗi kết nối khi cập nhật thông tin đăng nhập', 'danger');
            });
    });

    // Xử lý sự kiện nút Convert CSV
    convertCsvBtn.addEventListener('click', function() {
        csvFileInput.value = '';
        csvProgress.style.display = 'none';
        csvResult.style.display = 'none';
        downloadCsvResult.style.display = 'none';
        csvModal.show();
    });

    // Xử lý sự kiện nút Upload CSV
    uploadCsvBtn.addEventListener('click', function() {
        const file = csvFileInput.files[0];
        if (!file) {
            showAlert('Vui lòng chọn file CSV', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        csvProgress.style.display = 'block';
        csvResult.style.display = 'none';
        downloadCsvResult.style.display = 'none';

        fetch('/api/convert_csv', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Bắt đầu kiểm tra trạng thái
                    checkCsvConversionStatus(data.task_id);
                } else {
                    csvProgress.style.display = 'none';
                    showCsvResult('Lỗi: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Lỗi khi upload file CSV:', error);
                csvProgress.style.display = 'none';
                showCsvResult('Lỗi kết nối khi upload file', 'danger');
            });
    });

    // Xử lý sự kiện modal browse thư mục
    refreshFolderBtn.addEventListener('click', function() {
        loadFolderList(currentBrowsePath);
    });

    selectFolderBtn.addEventListener('click', function() {
        outputPathInput.value = currentBrowsePath;
        folderBrowserModal.hide();
        showAlert('Đã chọn thư mục: ' + currentBrowsePath, 'success');
    });

    createFolderBtn.addEventListener('click', function() {
        createNewFolder();
    });

    // Xử lý sự kiện click vào thư mục thông dụng
    document.querySelectorAll('[data-folder]').forEach(btn => {
        btn.addEventListener('click', function() {
            const folderType = this.getAttribute('data-folder');
            loadFolderList(folderType);
        });
    });

    // Raw Data Processing Event Listeners - Sử dụng event delegation
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'rawDataLoadSheets') {
            loadRawDataSheets();
        }
        if (e.target && e.target.id === 'rawDataLoadTargetSheets') {
            loadRawDataTargetSheets();
        }
        if (e.target && e.target.id === 'rawDataImportBtn') {
            startRawDataImport();
        }
        if (e.target && e.target.id === 'rawDataProcessBtn') {
            startRawDataProcess();
        }
    });

    // Xử lý sự kiện mở thư mục chứa file
    fileResultLink.addEventListener('click', function(e) {
        e.preventDefault();
        if (currentTaskId) {
            openFolderContainingFile(currentTaskId);
        }
    });

    // Hàm mở thư mục chứa file
    function openFolderContainingFile(taskId) {
        fetch('/api/open_folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                path: tasks[taskId].output_folder
            })
        })
            .then(response => response.json())
            .catch(error => {
                console.error('Lỗi khi mở thư mục:', error);
                showAlert('Không thể mở thư mục', 'warning');
            });
    }

    // Lưu trữ thông tin tác vụ cục bộ
    let tasks = {};

    // Hàm xử lý và định dạng ID shop
    function processAndFormatIds(text) {
        // Tách theo dòng và loại bỏ khoảng trắng
        const lines = text.split('\n').map(line => line.trim()).filter(line => line);

        // Loại bỏ trùng lặp
        const uniqueLines = [];
        const seen = new Set();
        for (const line of lines) {
            if (line && !seen.has(line)) {
                seen.add(line);
                uniqueLines.push(line);
            }
        }

        // Định dạng với số thứ tự
        const formattedText = uniqueLines.map((line, index) => `${index + 1}. ${line}`).join('\n');

        // Đặt vào textarea
        shopIdsTextarea.value = formattedText;
    }

    // Hàm bắt đầu xử lý dữ liệu
    function startProcessing() {
        // Lấy danh sách ID từ textarea có định dạng "1. 123456789"
        const text = shopIdsTextarea.value.trim();
        if (!text) {
            showAlert('Vui lòng nhập ít nhất một ID shop!', 'warning');
            return;
        }

        // Lọc lấy chỉ ID số, bỏ qua số thứ tự
        const shopIds = [];
        for (const line of text.split('\n')) {
            if (line.trim()) {
                // Tìm và lấy phần số ID từ dòng (loại bỏ số thứ tự nếu có)
                const match1 = line.match(/\d+\.\s*(\d+)/);
                const match2 = line.match(/(\d+)/);

                if (match1) {
                    shopIds.push(match1[1]);
                } else if (match2) {
                    shopIds.push(match2[1]);
                }
            }
        }

        if (!shopIds.length) {
            showAlert('Không tìm thấy ID shop hợp lệ!', 'warning');
            return;
        }

        // Lấy đường dẫn lưu file (không yêu cầu bắt buộc)
        const outputPath = outputPathInput.value.trim();

        // Kiểm tra tên file
        const filename = filenameInput.value.trim();
        if (!filename) {
            showAlert('Vui lòng nhập tên file!', 'warning');
            return;
        }

        // Chuyển sang bước 2
        step1.style.display = 'none';
        step2.style.display = 'block';

        // Xóa dữ liệu cũ
        progressTable.innerHTML = '';
        logContainer.innerHTML = '';
        progressBar.style.width = '0%';
        progressBar.textContent = '0%';
        shopCounter.textContent = `💻 Hoàn thành: 0/${shopIds.length}`;
        totalProducts.textContent = '📊 Tổng số sản phẩm: 0';
        fileResult.style.display = 'none';

        // Khởi tạo bảng tiến trình
        for (let i = 0; i < shopIds.length; i++) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center">${i + 1}</td>
                <td>${shopIds[i]}</td>
                <td class="status-waiting">⏳ Chờ xử lý</td>
                <td class="text-end">0</td>
            `;
            progressTable.appendChild(row);
        }

        // Bắt đầu timer
        startTime = Date.now();
        updateTimer();
        timerInterval = setInterval(updateTimer, 1000);

        // Gọi API để bắt đầu xử lý
        fetch('/api/start_scraping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                shop_ids: shopIds,
                output_path: outputPath,
                output_filename: filename
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    addLogMessage(`🚀 Bắt đầu quá trình xử lý dữ liệu...`);
                    addLogMessage(`🔄 Tiến độ: 0/${shopIds.length} shop đã hoàn thành`);

                    // Bắt đầu kiểm tra trạng thái
                    startStatusCheck();
                } else {
                    addLogMessage(`❌ Lỗi: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Lỗi khi bắt đầu xử lý:', error);
                addLogMessage(`❌ Lỗi kết nối: ${error.message}`);
                stopStatusCheck();
            });
    }

    // Hàm kiểm tra trạng thái xử lý định kỳ
    function startStatusCheck() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }

        statusCheckInterval = setInterval(() => {
            if (!currentTaskId) return;

            fetch(`/api/get_task_status/${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Lưu thông tin tác vụ cục bộ
                        tasks[currentTaskId] = data.task;
                        updateTaskStatus(data.task);
                    } else {
                        console.error('Không tìm thấy tác vụ:', data.message);
                        stopStatusCheck();
                    }
                })
                .catch(error => {
                    console.error('Lỗi khi kiểm tra trạng thái:', error);
                });
        }, 1000);
    }

    // Hàm dừng kiểm tra trạng thái
    function stopStatusCheck() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }

        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
    }

    // Hàm cập nhật trạng thái từ API
    function updateTaskStatus(task) {
        // Cập nhật tiến trình
        const percent = task.progress_percent;
        progressBar.style.width = `${percent}%`;
        progressBar.textContent = `${percent}%`;
        progressBar.setAttribute('aria-valuenow', percent);

        // Cập nhật số shop đã hoàn thành
        shopCounter.textContent = `💻 Hoàn thành: ${task.completed_shops}/${task.total_shops}`;

        // Cập nhật bảng tiến trình
        for (const [shopId, status] of Object.entries(task.shop_statuses)) {
            const shopIndex = task.shop_ids.indexOf(shopId);
            if (shopIndex !== -1) {
                const row = progressTable.children[shopIndex];
                if (row) {
                    const statusCell = row.children[2];
                    const countCell = row.children[3];

                    // Xóa tất cả status classes cũ
                    statusCell.className = '';

                    // Đặt status class mới và text tương ứng
                    switch (status.status) {
                        case 'waiting':
                            statusCell.className = 'status-waiting';
                            statusCell.textContent = '⏳ Chờ xử lý';
                            break;
                        case 'processing':
                            statusCell.className = 'status-processing';
                            statusCell.textContent = '🔄 Đang xử lý';
                            break;
                        case 'completed':
                            statusCell.className = 'status-completed';
                            statusCell.textContent = '✅ Hoàn thành';
                            break;
                        case 'error':
                            statusCell.className = 'status-error';
                            statusCell.textContent = '❌ Lỗi';
                            break;
                        case 'no_products':
                            statusCell.className = 'status-no-products';
                            statusCell.textContent = 'ℹ️ Shop không có dữ liệu';
                            break;
                    }

                    // Cập nhật số sản phẩm
                    if (status.product_count > 0) {
                        countCell.textContent = status.product_count;
                    }
                }
            }
        }

        // Cập nhật tổng số sản phẩm
        totalProducts.textContent = `📊 Tổng số sản phẩm: ${task.total_products.toLocaleString('vi-VN')}`;

        // Cập nhật log
        const currentLogCount = logContainer.children.length;
        for (let i = currentLogCount; i < task.log_messages.length; i++) {
            addLogMessage(task.log_messages[i]);
        }

        // Kiểm tra nếu tác vụ đã hoàn thành
        if (task.status === 'completed' || task.status === 'error') {
            stopStatusCheck();

            if (task.status === 'completed') {
                addLogMessage(`✅ Xử lý dữ liệu thành công. Tổng số: ${task.total_products} sản phẩm.`);
                showAlert(`Đã lưu ${task.total_products.toLocaleString('vi-VN')} sản phẩm thành công!`, 'success');

                // Hiển thị thông tin file
                fileResultText.textContent = `File kết quả đã được lưu tại: ${task.output_file}`;
                fileResult.style.display = 'block';
            } else {
                addLogMessage('❌ Xử lý dữ liệu thất bại.');
                showAlert('Xử lý dữ liệu thất bại', 'danger');
            }
        }
    }

    // Hàm kiểm tra trạng thái chuyển đổi CSV
    function checkCsvConversionStatus(taskId) {
        const checkInterval = setInterval(() => {
            fetch(`/api/get_task_status/${taskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const task = data.task;
                        // Lưu thông tin tác vụ cục bộ
                        tasks[taskId] = task;

                        // Kiểm tra nếu tác vụ đã hoàn thành
                        if (task.status === 'completed' || task.status === 'error') {
                            clearInterval(checkInterval);
                            csvProgress.style.display = 'none';

                            if (task.status === 'completed') {
                                showCsvResult('✅ Chuyển đổi thành công!', 'success');
                                downloadCsvResult.href = `/api/get_file/${taskId}`;
                                downloadCsvResult.style.display = 'block';
                            } else {
                                const errorMessage = task.log_messages.find(msg => msg.startsWith('❌')) || 'Lỗi không xác định';
                                showCsvResult(errorMessage, 'danger');
                            }
                        }
                    } else {
                        clearInterval(checkInterval);
                        csvProgress.style.display = 'none';
                        showCsvResult('Không thể kiểm tra trạng thái chuyển đổi', 'danger');
                    }
                })
                .catch(error => {
                    clearInterval(checkInterval);
                    csvProgress.style.display = 'none';
                    showCsvResult(`Lỗi kết nối: ${error.message}`, 'danger');
                });
        }, 1000);
    }

    // Hàm hiển thị kết quả chuyển đổi CSV
    function showCsvResult(message, type) {
        csvResult.className = `alert alert-${type} mt-3`;
        csvResult.textContent = message;
        csvResult.style.display = 'block';
    }

    // Hàm thêm log message
    function addLogMessage(message) {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        // Thêm class dựa vào loại log
        if (message.startsWith('❌')) {
            logEntry.classList.add('error');
        } else if (message.startsWith('⚠️')) {
            logEntry.classList.add('warning');
        } else if (message.startsWith('✅')) {
            logEntry.classList.add('success');
        } else if (message.startsWith('ℹ️') || message.startsWith('🔄')) {
            logEntry.classList.add('info');
        }

        logEntry.textContent = message;
        logContainer.appendChild(logEntry);

        // Cuộn xuống để hiển thị message mới nhất
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // Hàm cập nhật thời gian
    function updateTimer() {
        if (!startTime) return;

        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;

        timerLabel.textContent = `⏱️ Thời gian: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Hàm hiển thị thông báo
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
        alertDiv.style.zIndex = '9999';
        alertDiv.style.maxWidth = '80%';

        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(alertDiv);

        // Tự động ẩn sau 5 giây
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Hàm tải danh sách thư mục
    function loadFolderList(path) {
        folderLoading.style.display = 'block';
        folderList.innerHTML = '';

        fetch('/api/list_folders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ path: path })
        })
            .then(response => response.json())
            .then(data => {
                folderLoading.style.display = 'none';

                if (data.success) {
                    currentBrowsePath = data.current_path;
                    currentPathInput.value = currentBrowsePath;

                    // Xóa danh sách cũ
                    folderList.innerHTML = '';

                    // Thêm nút "Lên thư mục cha" nếu có
                    if (data.parent_path !== null) {
                        const parentItem = document.createElement('div');
                        parentItem.className = 'folder-item d-flex align-items-center p-2 border-bottom';
                        parentItem.style.cursor = 'pointer';
                        parentItem.innerHTML = `
                            <i class="fas fa-level-up-alt me-2 text-secondary"></i>
                            <span class="text-secondary">.. (Thư mục cha)</span>
                        `;
                        parentItem.addEventListener('click', function() {
                            loadFolderList(data.parent_path);
                        });
                        folderList.appendChild(parentItem);
                    }

                    // Thêm các thư mục con
                    data.folders.forEach(folder => {
                        const folderItem = document.createElement('div');
                        folderItem.className = 'folder-item d-flex align-items-center p-2 border-bottom';
                        folderItem.style.cursor = 'pointer';
                        folderItem.innerHTML = `
                            <i class="fas fa-folder me-2 text-warning"></i>
                            <span>${folder.name}</span>
                        `;

                        folderItem.addEventListener('click', function() {
                            loadFolderList(folder.path);
                        });

                        // Hover effect
                        folderItem.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#f8f9fa';
                        });
                        folderItem.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '';
                        });

                        folderList.appendChild(folderItem);
                    });

                    // Nếu không có thư mục nào
                    if (data.folders.length === 0 && data.parent_path === null) {
                        const emptyItem = document.createElement('div');
                        emptyItem.className = 'text-center p-3 text-muted';
                        emptyItem.textContent = 'Không có thư mục con nào';
                        folderList.appendChild(emptyItem);
                    }
                } else {
                    folderList.innerHTML = `
                        <div class="text-center p-3 text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                folderLoading.style.display = 'none';
                console.error('Lỗi khi tải danh sách thư mục:', error);
                folderList.innerHTML = `
                    <div class="text-center p-3 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Lỗi kết nối: ${error.message}
                    </div>
                `;
            });
    }

    // Hàm tạo thư mục mới
    function createNewFolder() {
        const folderName = prompt('Nhập tên thư mục mới:', '');

        if (!folderName || !folderName.trim()) {
            return;
        }

        const trimmedName = folderName.trim();

        // Kiểm tra tên thư mục hợp lệ
        if (trimmedName.includes('/') || trimmedName.includes('\\') || trimmedName === '.' || trimmedName === '..') {
            showAlert('Tên thư mục không hợp lệ. Không được chứa ký tự / \\ hoặc là . ..', 'warning');
            return;
        }

        // Gọi API tạo thư mục
        fetch('/api/create_folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                parent_path: currentBrowsePath,
                folder_name: trimmedName
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    // Tải lại danh sách thư mục để hiển thị thư mục mới
                    loadFolderList(currentBrowsePath);
                } else {
                    showAlert('Lỗi: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Lỗi khi tạo thư mục:', error);
                showAlert('Lỗi kết nối khi tạo thư mục', 'danger');
            });
    }

    // ===== RAW DATA PROCESSING FUNCTIONS =====

    function extractSpreadsheetId(urlOrId) {
        if (!urlOrId) return null;

        // Patterns to extract ID from URL
        const patterns = [
            /https:\/\/docs\.google\.com\/spreadsheets\/d\/([a-zA-Z0-9_-]+)/,
            /https:\/\/docs\.google\.com\/spreadsheets\/d\/e\/([a-zA-Z0-9_-]+)\/pubhtml/,
            /([a-zA-Z0-9_-]{25,})/
        ];

        for (const pattern of patterns) {
            const match = urlOrId.match(pattern);
            if (match) {
                return match[1];
            }
        }

        // If no pattern matches, assume it's already an ID
        return urlOrId;
    }

    function loadRawDataSheets() {
        const rawDataSourceUrl = document.getElementById('rawDataSourceUrl');
        const rawDataLoadSheets = document.getElementById('rawDataLoadSheets');

        const url = rawDataSourceUrl.value.trim();
        if (!url) {
            showAlert('Vui lòng nhập URL spreadsheet nguồn', 'warning');
            return;
        }

        // Auto-parse URL to ID if it's a Google Sheets URL
        const parsedId = extractSpreadsheetId(url);
        if (parsedId && parsedId !== url) {
            rawDataSourceUrl.value = parsedId;
            console.log(`Auto-parsed URL to ID: ${parsedId}`);
        }

        rawDataLoadSheets.disabled = true;
        rawDataLoadSheets.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

        fetch('/api/raw_data/get_sheets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_url: rawDataSourceUrl.value.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            const rawDataLoadSheets = document.getElementById('rawDataLoadSheets');
            const rawDataTargetUrl = document.getElementById('rawDataTargetUrl');

            rawDataLoadSheets.disabled = false;
            rawDataLoadSheets.innerHTML = '<i class="fas fa-download"></i> Load Sheets';

            if (data.success) {
                rawDataSourceSpreadsheetId = data.spreadsheet_id;
                displayRawDataSheets(data.sheets);
                showAlert(`Đã tải ${data.sheets.length} sheets`, 'success');

                // Auto-fill target URL if empty
                if (!rawDataTargetUrl.value.trim()) {
                    rawDataTargetUrl.value = rawDataSourceUrl.value.trim();
                    loadRawDataTargetSheets();
                }
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            const rawDataLoadSheets = document.getElementById('rawDataLoadSheets');
            rawDataLoadSheets.disabled = false;
            rawDataLoadSheets.innerHTML = '<i class="fas fa-download"></i> Load Sheets';
            console.error('Error loading sheets:', error);
            showAlert('Lỗi kết nối khi tải sheets', 'danger');
        });
    }

    function displayRawDataSheets(sheets) {
        const rawDataSheetList = document.getElementById('rawDataSheetList');
        rawDataSheetList.innerHTML = '';
        rawDataSelectedSheets = [];

        sheets.forEach((sheet, index) => {
            const colClass = index % 3 === 0 ? 'col-md-4' : index % 3 === 1 ? 'col-md-4' : 'col-md-4';
            const checkboxHtml = `
                <div class="${colClass} mb-2">
                    <div class="form-check">
                        <input class="form-check-input raw-data-sheet-checkbox" type="checkbox" value="${sheet}" id="rawDataSheet${index}">
                        <label class="form-check-label" for="rawDataSheet${index}">
                            ${sheet}
                        </label>
                    </div>
                </div>
            `;
            rawDataSheetList.innerHTML += checkboxHtml;
        });

        // Add event listeners to checkboxes
        document.querySelectorAll('.raw-data-sheet-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    rawDataSelectedSheets.push(this.value);
                } else {
                    const index = rawDataSelectedSheets.indexOf(this.value);
                    if (index > -1) {
                        rawDataSelectedSheets.splice(index, 1);
                    }
                }
            });
        });
    }

    function loadRawDataTargetSheets() {
        const rawDataTargetUrl = document.getElementById('rawDataTargetUrl');
        const rawDataSourceUrl = document.getElementById('rawDataSourceUrl');
        const rawDataLoadTargetSheets = document.getElementById('rawDataLoadTargetSheets');

        const url = rawDataTargetUrl.value.trim() || rawDataSourceUrl.value.trim();
        if (!url) {
            showAlert('Vui lòng nhập URL spreadsheet đích', 'warning');
            return;
        }

        // Auto-parse URL to ID if it's a Google Sheets URL
        const parsedId = extractSpreadsheetId(url);
        if (parsedId && parsedId !== url) {
            rawDataTargetUrl.value = parsedId;
            console.log(`Auto-parsed target URL to ID: ${parsedId}`);
        }

        rawDataLoadTargetSheets.disabled = true;
        rawDataLoadTargetSheets.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        fetch('/api/raw_data/get_sheets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_url: rawDataTargetUrl.value.trim() || rawDataSourceUrl.value.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            const rawDataLoadTargetSheets = document.getElementById('rawDataLoadTargetSheets');
            const rawDataTargetSheet = document.getElementById('rawDataTargetSheet');

            rawDataLoadTargetSheets.disabled = false;
            rawDataLoadTargetSheets.innerHTML = '<i class="fas fa-sync"></i> Load';

            if (data.success) {
                rawDataTargetSpreadsheetId = data.spreadsheet_id;

                // Clear and populate target sheet dropdown
                rawDataTargetSheet.innerHTML = '<option value="">Chọn sheet đích...</option>';
                data.sheets.forEach(sheet => {
                    const option = document.createElement('option');
                    option.value = sheet;
                    option.textContent = sheet;
                    rawDataTargetSheet.appendChild(option);
                });

                // Auto-select "Pool Deal" if exists
                const poolDealOption = Array.from(rawDataTargetSheet.options).find(option => option.value === 'Pool Deal');
                if (poolDealOption) {
                    poolDealOption.selected = true;
                }

                showAlert(`Đã tải ${data.sheets.length} target sheets`, 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            const rawDataLoadTargetSheets = document.getElementById('rawDataLoadTargetSheets');
            rawDataLoadTargetSheets.disabled = false;
            rawDataLoadTargetSheets.innerHTML = '<i class="fas fa-sync"></i> Load';
            console.error('Error loading target sheets:', error);
            showAlert('Lỗi kết nối khi tải target sheets', 'danger');
        });
    }

    function startRawDataImport() {
        if (!rawDataSourceSpreadsheetId) {
            showAlert('Vui lòng load sheets nguồn trước', 'warning');
            return;
        }

        if (rawDataSelectedSheets.length === 0) {
            showAlert('Vui lòng chọn ít nhất một sheet nguồn', 'warning');
            return;
        }

        const rawDataTargetSheet = document.getElementById('rawDataTargetSheet');
        const rawDataProgress = document.getElementById('rawDataProgress');
        const rawDataProgressBar = document.getElementById('rawDataProgressBar');
        const rawDataStatus = document.getElementById('rawDataStatus');
        const rawDataLog = document.getElementById('rawDataLog');
        const rawDataImportBtn = document.getElementById('rawDataImportBtn');

        const targetSheet = rawDataTargetSheet.value || 'Pool Deal';
        const mode = document.querySelector('input[name="rawDataMode"]:checked').value;

        // Show progress
        rawDataProgress.style.display = 'block';
        rawDataProgressBar.style.width = '0%';
        rawDataProgressBar.textContent = '0%';
        rawDataStatus.textContent = 'Đang bắt đầu import...';
        rawDataLog.innerHTML = '';

        rawDataImportBtn.disabled = true;
        rawDataImportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang Import...';

        addRawDataLog('🚀 Bắt đầu import dữ liệu...');
        addRawDataLog(`📊 Chế độ: ${mode === 'copy' ? 'Tạo Mới' : 'Bổ Sung'}`);
        addRawDataLog(`📁 Sheets nguồn: ${rawDataSelectedSheets.join(', ')}`);
        addRawDataLog(`🎯 Sheet đích: ${targetSheet}`);

        fetch('/api/raw_data/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                source_spreadsheet_id: rawDataSourceSpreadsheetId,
                target_spreadsheet_id: rawDataTargetSpreadsheetId || rawDataSourceSpreadsheetId,
                selected_sheets: rawDataSelectedSheets,
                target_sheet: targetSheet,
                mode: mode
            })
        })
        .then(response => response.json())
        .then(data => {
            rawDataImportBtn.disabled = false;
            rawDataImportBtn.innerHTML = '<i class="fas fa-play"></i> Bắt Đầu Import';

            if (data.success) {
                rawDataProgressBar.style.width = '100%';
                rawDataProgressBar.textContent = '100%';
                rawDataStatus.textContent = 'Import hoàn thành!';
                addRawDataLog('✅ ' + data.message);
                showAlert('Import dữ liệu thành công!', 'success');
            } else {
                rawDataProgressBar.classList.add('bg-danger');
                rawDataStatus.textContent = 'Import thất bại!';
                addRawDataLog('❌ ' + data.message);
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            rawDataImportBtn.disabled = false;
            rawDataImportBtn.innerHTML = '<i class="fas fa-play"></i> Bắt Đầu Import';
            rawDataProgressBar.classList.add('bg-danger');
            rawDataStatus.textContent = 'Import thất bại!';
            addRawDataLog('❌ Lỗi kết nối: ' + error.message);
            console.error('Error importing data:', error);
            showAlert('Lỗi kết nối khi import dữ liệu', 'danger');
        });
    }

    function startRawDataProcess() {
        if (!rawDataTargetSpreadsheetId) {
            showAlert('Vui lòng load target spreadsheet trước', 'warning');
            return;
        }

        const rawDataTargetSheet = document.getElementById('rawDataTargetSheet');
        const rawDataProcessBtn = document.getElementById('rawDataProcessBtn');

        const targetSheet = rawDataTargetSheet.value || 'Pool Deal';

        rawDataProcessBtn.disabled = true;
        rawDataProcessBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang Xử Lý...';

        addRawDataLog('🔧 Bắt đầu xử lý dữ liệu...');

        fetch('/api/raw_data/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_id: rawDataTargetSpreadsheetId,
                sheet_name: targetSheet,
                rules: []
            })
        })
        .then(response => response.json())
        .then(data => {
            const rawDataProcessBtn = document.getElementById('rawDataProcessBtn');
            rawDataProcessBtn.disabled = false;
            rawDataProcessBtn.innerHTML = '<i class="fas fa-cogs"></i> Xử Lý Dữ Liệu';

            if (data.success) {
                addRawDataLog('✅ ' + data.message);
                showAlert('Xử lý dữ liệu thành công!', 'success');
            } else {
                addRawDataLog('❌ ' + data.message);
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            const rawDataProcessBtn = document.getElementById('rawDataProcessBtn');
            rawDataProcessBtn.disabled = false;
            rawDataProcessBtn.innerHTML = '<i class="fas fa-cogs"></i> Xử Lý Dữ Liệu';
            addRawDataLog('❌ Lỗi kết nối: ' + error.message);
            console.error('Error processing data:', error);
            showAlert('Lỗi kết nối khi xử lý dữ liệu', 'danger');
        });
    }

    function addRawDataLog(message) {
        const rawDataLog = document.getElementById('rawDataLog');
        if (rawDataLog) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            rawDataLog.appendChild(logEntry);
            rawDataLog.scrollTop = rawDataLog.scrollHeight;
        }
    }

    // ===== DATA PROCESSING MODAL FUNCTIONS =====

    // Variables for data processing
    let processSpreadsheetId = '';
    let processSheetName = '';
    let availableHeaders = [];
    let processingRules = [];
    let currentRuleIndex = 0;

    // Condition types
    const conditionTypes = [
        "Clear 'X' Values",
        "Format Numbers",
        "Format Percentages",
        "Fix Empty Rows"
    ];

    // Event listeners for data processing modal
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'loadProcessSheets') {
            loadProcessSheets();
        }
        if (e.target && e.target.id === 'addRuleBtn') {
            addProcessingRule();
        }
        if (e.target && e.target.id === 'startProcessingBtn') {
            startDataProcessing();
        }
        if (e.target && e.target.classList.contains('select-headers-btn')) {
            const ruleIndex = parseInt(e.target.dataset.ruleIndex);
            openHeaderSelectionModal(ruleIndex);
        }
        if (e.target && e.target.classList.contains('remove-rule-btn')) {
            const ruleIndex = parseInt(e.target.dataset.ruleIndex);
            removeProcessingRule(ruleIndex);
        }
        if (e.target && e.target.id === 'selectAllHeaders') {
            toggleAllHeaders(true);
        }
        if (e.target && e.target.id === 'deselectAllHeaders') {
            toggleAllHeaders(false);
        }
        if (e.target && e.target.id === 'confirmHeaderSelection') {
            confirmHeaderSelection();
        }
    });

    // Auto-fill process spreadsheet when modal opens
    document.getElementById('processDataModal').addEventListener('show.bs.modal', function() {
        const processSpreadsheetUrl = document.getElementById('processSpreadsheetUrl');
        const processSheetSelect = document.getElementById('processSheetSelect');
        const rawDataTargetUrl = document.getElementById('rawDataTargetUrl');
        const rawDataTargetSheet = document.getElementById('rawDataTargetSheet');

        // Auto-fill from target URL if available
        const targetUrl = rawDataTargetUrl.value.trim() || (rawDataTargetSpreadsheetId || rawDataSourceSpreadsheetId);
        const targetSheet = rawDataTargetSheet.value.trim() || 'Pool Deal';

        if (targetUrl) {
            // Check if we need to load new data or can reuse existing
            const needsReload = processSpreadsheetUrl.value.trim() !== targetUrl ||
                               processSheetName !== targetSheet ||
                               !availableHeaders.length;

            processSpreadsheetUrl.value = targetUrl;
            processSpreadsheetId = targetUrl;

            if (needsReload) {
                console.log('🔄 Auto-loading sheets and headers from step 1...');
                autoLoadFromStep1(targetUrl, targetSheet);
            } else {
                console.log('✅ Reusing existing data from previous load');
                // Just ensure UI is properly set
                if (processSheetSelect.value !== targetSheet) {
                    // Try to select the target sheet
                    const targetOption = Array.from(processSheetSelect.options).find(option => option.value === targetSheet);
                    if (targetOption) {
                        processSheetSelect.value = targetSheet;
                        processSheetName = targetSheet;
                    }
                }

                // Enable buttons if we have data
                if (availableHeaders.length) {
                    document.getElementById('addRuleBtn').disabled = false;
                    document.getElementById('startProcessingBtn').disabled = false;

                    // Create default rule if no rules exist
                    if (processingRules.length === 0) {
                        addProcessingRule();
                    }
                }
            }
        } else {
            // Reset if no target data available
            resetProcessingRules(true);
        }
    });

    function autoLoadFromStep1(spreadsheetId, targetSheet) {
        // First load sheets
        fetch('/api/raw_data/get_sheets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_url: spreadsheetId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const processSheetSelect = document.getElementById('processSheetSelect');
                processSheetSelect.innerHTML = '<option value="">Chọn sheet...</option>';

                data.sheets.forEach(sheet => {
                    const option = document.createElement('option');
                    option.value = sheet;
                    option.textContent = sheet;
                    processSheetSelect.appendChild(option);
                });

                // Try to select target sheet
                const targetOption = Array.from(processSheetSelect.options).find(option => option.value === targetSheet);
                if (targetOption) {
                    processSheetSelect.value = targetSheet;
                    processSheetName = targetSheet;

                    // Auto-load headers for the target sheet
                    autoLoadHeaders(spreadsheetId, targetSheet);
                } else {
                    console.log(`⚠️ Target sheet '${targetSheet}' not found, using first available sheet`);
                    if (data.sheets.length > 0) {
                        processSheetSelect.value = data.sheets[0];
                        processSheetName = data.sheets[0];
                        autoLoadHeaders(spreadsheetId, data.sheets[0]);
                    }
                }

                console.log(`📊 Auto-loaded ${data.sheets.length} sheets`);
            } else {
                console.error('Failed to auto-load sheets:', data.message);
                showAlert('Không thể tự động tải sheets: ' + data.message, 'warning');
            }
        })
        .catch(error => {
            console.error('Error auto-loading sheets:', error);
            showAlert('Lỗi khi tự động tải sheets', 'warning');
        });
    }

    function autoLoadHeaders(spreadsheetId, sheetName) {
        fetch('/api/raw_data/get_headers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_id: spreadsheetId,
                sheet_name: sheetName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableHeaders = data.headers;
                console.log(`📋 Auto-loaded ${availableHeaders.length} headers`);

                // Enable buttons
                document.getElementById('addRuleBtn').disabled = false;
                document.getElementById('startProcessingBtn').disabled = false;

                // Create default rule if no rules exist
                if (processingRules.length === 0) {
                    addProcessingRule();
                }

                showAlert(`✅ Tự động tải ${availableHeaders.length} headers từ step 1`, 'success');
            } else {
                console.error('Failed to auto-load headers:', data.message);
                showAlert('Không thể tự động tải headers: ' + data.message, 'warning');
            }
        })
        .catch(error => {
            console.error('Error auto-loading headers:', error);
            showAlert('Lỗi khi tự động tải headers', 'warning');
        });
    }

    function loadProcessSheets() {
        const processSpreadsheetUrl = document.getElementById('processSpreadsheetUrl');
        const loadProcessSheets = document.getElementById('loadProcessSheets');

        const url = processSpreadsheetUrl.value.trim();
        if (!url) {
            showAlert('Vui lòng nhập URL spreadsheet cần xử lý', 'warning');
            return;
        }

        // Auto-parse URL to ID
        const parsedId = extractSpreadsheetId(url);
        if (parsedId && parsedId !== url) {
            processSpreadsheetUrl.value = parsedId;
            console.log(`Auto-parsed process URL to ID: ${parsedId}`);
        }

        processSpreadsheetId = processSpreadsheetUrl.value.trim();

        // Reset existing data since user is manually loading new spreadsheet
        resetProcessingRules(true);

        loadProcessSheets.disabled = true;
        loadProcessSheets.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

        console.log('🔄 Manual load: Loading sheets for new spreadsheet...');

        fetch('/api/raw_data/get_sheets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_url: processSpreadsheetId
            })
        })
        .then(response => response.json())
        .then(data => {
            loadProcessSheets.disabled = false;
            loadProcessSheets.innerHTML = '<i class="fas fa-download"></i> Load Sheet';

            if (data.success) {
                const processSheetSelect = document.getElementById('processSheetSelect');
                processSheetSelect.innerHTML = '<option value="">Chọn sheet...</option>';

                data.sheets.forEach(sheet => {
                    const option = document.createElement('option');
                    option.value = sheet;
                    option.textContent = sheet;
                    processSheetSelect.appendChild(option);
                });

                // Auto-select "Pool Deal" if exists
                const poolDealOption = Array.from(processSheetSelect.options).find(option => option.value === 'Pool Deal');
                if (poolDealOption) {
                    poolDealOption.selected = true;
                    processSheetName = 'Pool Deal';
                    loadProcessHeaders();
                } else {
                    // Clear sheet name if Pool Deal not found
                    processSheetName = '';
                }

                showAlert(`Đã tải ${data.sheets.length} sheets`, 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            loadProcessSheets.disabled = false;
            loadProcessSheets.innerHTML = '<i class="fas fa-download"></i> Load Sheet';
            console.error('Error loading process sheets:', error);
            showAlert('Lỗi kết nối khi tải sheets', 'danger');
        });
    }

    // Event listener for sheet selection change
    document.addEventListener('change', function(e) {
        if (e.target && e.target.id === 'processSheetSelect') {
            processSheetName = e.target.value;
            if (processSheetName && processSpreadsheetId) {
                console.log(`📋 Manual sheet change: Loading headers for '${processSheetName}'`);
                loadProcessHeaders();
            }
        }
    });

    // Event listener for manual URL changes
    document.addEventListener('input', function(e) {
        if (e.target && e.target.id === 'processSpreadsheetUrl') {
            const currentUrl = e.target.value.trim();

            // If user clears the URL or changes it significantly, reset the state
            if (!currentUrl || currentUrl !== processSpreadsheetId) {
                console.log('🔄 URL changed manually, will need to reload on next Load Sheet click');

                // Don't auto-reset immediately, just mark that we need to reload
                // The actual reset will happen when user clicks "Load Sheet"

                // Clear sheet selection if URL is completely different
                if (currentUrl && processSpreadsheetId && !currentUrl.includes(processSpreadsheetId.substring(0, 10))) {
                    const processSheetSelect = document.getElementById('processSheetSelect');
                    processSheetSelect.innerHTML = '<option value="">Chọn sheet...</option>';
                    processSheetName = '';

                    // Disable buttons until new data is loaded
                    document.getElementById('addRuleBtn').disabled = true;
                    document.getElementById('startProcessingBtn').disabled = true;
                }
            }
        }
    });

    function loadProcessHeaders() {
        if (!processSpreadsheetId || !processSheetName) {
            return;
        }

        fetch('/api/raw_data/get_headers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_id: processSpreadsheetId,
                sheet_name: processSheetName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableHeaders = data.headers;
                console.log(`Loaded ${availableHeaders.length} headers for processing`);

                // Enable add rule button and start processing button
                document.getElementById('addRuleBtn').disabled = false;
                document.getElementById('startProcessingBtn').disabled = false;

                // Create default rule if no rules exist
                if (processingRules.length === 0) {
                    addProcessingRule();
                }

                showAlert(`Đã tải ${availableHeaders.length} headers`, 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading headers:', error);
            showAlert('Lỗi kết nối khi tải headers', 'danger');
        });
    }

    function resetProcessingRules(clearAll = false) {
        processingRules = [];
        const rulesContainer = document.getElementById('rulesContainer');
        rulesContainer.innerHTML = '';

        if (clearAll) {
            // Full reset - disable buttons and clear data
            document.getElementById('addRuleBtn').disabled = true;
            document.getElementById('startProcessingBtn').disabled = true;
            availableHeaders = [];
            processSheetName = '';
        } else {
            // Soft reset - keep buttons enabled if we have headers
            if (!availableHeaders.length) {
                document.getElementById('addRuleBtn').disabled = true;
                document.getElementById('startProcessingBtn').disabled = true;
            }
        }
    }

    function addProcessingRule() {
        if (!availableHeaders.length) {
            showAlert('Vui lòng tải headers trước', 'warning');
            return;
        }

        const ruleIndex = processingRules.length;
        const rule = {
            index: ruleIndex,
            condition: conditionTypes[0],
            selectedHeaders: []
        };

        processingRules.push(rule);
        renderRule(rule);
    }

    function renderRule(rule) {
        const rulesContainer = document.getElementById('rulesContainer');

        const ruleDiv = document.createElement('div');
        ruleDiv.className = 'rule-item mb-3 p-3 border rounded';
        ruleDiv.dataset.ruleIndex = rule.index;

        ruleDiv.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-3">
                    <select class="form-select condition-select" data-rule-index="${rule.index}">
                        ${conditionTypes.map(type =>
                            `<option value="${type}" ${type === rule.condition ? 'selected' : ''}>${type}</option>`
                        ).join('')}
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-primary select-headers-btn" data-rule-index="${rule.index}">
                        Chọn Headers
                    </button>
                </div>
                <div class="col-md-4">
                    <span class="headers-count badge bg-info">${rule.selectedHeaders.length} headers được chọn</span>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-danger btn-sm remove-rule-btn" data-rule-index="${rule.index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        rulesContainer.appendChild(ruleDiv);

        // Add event listener for condition change
        const conditionSelect = ruleDiv.querySelector('.condition-select');
        conditionSelect.addEventListener('change', function() {
            rule.condition = this.value;
        });
    }

    function removeProcessingRule(ruleIndex) {
        if (processingRules.length <= 1) {
            showAlert('Phải có ít nhất một quy tắc', 'warning');
            return;
        }

        // Remove from array
        processingRules = processingRules.filter(rule => rule.index !== ruleIndex);

        // Re-render all rules
        const rulesContainer = document.getElementById('rulesContainer');
        rulesContainer.innerHTML = '';

        // Update indices and re-render
        processingRules.forEach((rule, index) => {
            rule.index = index;
            renderRule(rule);
        });
    }

    function openHeaderSelectionModal(ruleIndex) {
        if (!availableHeaders.length) {
            showAlert('Không có headers để chọn', 'warning');
            return;
        }

        currentRuleIndex = ruleIndex;
        const rule = processingRules.find(r => r.index === ruleIndex);

        // Populate headers in modal
        const headersContainer = document.getElementById('headersContainer');
        headersContainer.innerHTML = '';

        availableHeaders.forEach(header => {
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'form-check mb-2';

            const isChecked = rule.selectedHeaders.includes(header);
            checkboxDiv.innerHTML = `
                <input class="form-check-input header-checkbox" type="checkbox" value="${header}"
                       id="header_${header.replace(/[^a-zA-Z0-9]/g, '_')}" ${isChecked ? 'checked' : ''}>
                <label class="form-check-label" for="header_${header.replace(/[^a-zA-Z0-9]/g, '_')}">
                    ${header}
                </label>
            `;

            headersContainer.appendChild(checkboxDiv);
        });

        updateSelectedCount();

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('selectHeadersModal'));
        modal.show();
    }

    function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.header-checkbox');
        const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        document.getElementById('selectedCountLabel').textContent = `Đã chọn: ${selectedCount} headers`;
    }

    function toggleAllHeaders(checked) {
        const checkboxes = document.querySelectorAll('.header-checkbox');
        checkboxes.forEach(cb => cb.checked = checked);
        updateSelectedCount();
    }

    function confirmHeaderSelection() {
        const rule = processingRules.find(r => r.index === currentRuleIndex);
        const checkboxes = document.querySelectorAll('.header-checkbox');

        rule.selectedHeaders = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        // Update UI
        const ruleDiv = document.querySelector(`[data-rule-index="${currentRuleIndex}"]`);
        const countBadge = ruleDiv.querySelector('.headers-count');
        countBadge.textContent = `${rule.selectedHeaders.length} headers được chọn`;

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('selectHeadersModal'));
        modal.hide();
    }

    function startDataProcessing() {
        if (!processSpreadsheetId || !processSheetName) {
            showAlert('Vui lòng chọn spreadsheet và sheet cần xử lý', 'warning');
            return;
        }

        if (!processingRules.length) {
            showAlert('Vui lòng thêm ít nhất một quy tắc xử lý', 'warning');
            return;
        }

        // Validate rules have headers selected
        const invalidRules = processingRules.filter(rule => !rule.selectedHeaders.length);
        if (invalidRules.length) {
            showAlert('Một số quy tắc chưa có headers được chọn', 'warning');
            return;
        }

        const startProcessingBtn = document.getElementById('startProcessingBtn');
        startProcessingBtn.disabled = true;
        startProcessingBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang Xử Lý...';

        const rules = processingRules.map(rule => ({
            condition: rule.condition,
            headers: rule.selectedHeaders
        }));

        fetch('/api/raw_data/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                spreadsheet_id: processSpreadsheetId,
                sheet_name: processSheetName,
                rules: rules
            })
        })
        .then(response => response.json())
        .then(data => {
            startProcessingBtn.disabled = false;
            startProcessingBtn.innerHTML = '<i class="fas fa-play"></i> Bắt Đầu Xử Lý';

            if (data.success) {
                showAlert('Xử lý dữ liệu thành công!', 'success');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('processDataModal'));
                modal.hide();

                // Reset rules (soft reset to keep existing data if available)
                resetProcessingRules(false);
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            startProcessingBtn.disabled = false;
            startProcessingBtn.innerHTML = '<i class="fas fa-play"></i> Bắt Đầu Xử Lý';
            console.error('Error processing data:', error);
            showAlert('Lỗi kết nối khi xử lý dữ liệu', 'danger');
        });
    }

    // Add event listener for header checkboxes
    document.addEventListener('change', function(e) {
        if (e.target && e.target.classList.contains('header-checkbox')) {
            updateSelectedCount();
        }
    });

    // ===== INTERNAL DATA EVENT LISTENERS =====

    // Sheet type change
    document.addEventListener('change', function(e) {
        if (e.target && e.target.name === 'internalSheetType') {
            handleInternalSheetTypeChange();
        }
        if (e.target && e.target.name === 'internalCopyMode') {
            handleInternalCopyModeChange();
        }
    });

    // Internal Data buttons
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'internalLoadSheets') {
            loadInternalDataSheets();
        }
        if (e.target && e.target.id === 'internalToggleAll') {
            toggleAllInternalSheets();
        }
        if (e.target && e.target.id === 'internalCopyBtn') {
            startInternalDataCopy();
        }
        if (e.target && e.target.id === 'internalGetData') {
            getInternalDataFormulas();
        }
        if (e.target && e.target.id === 'internalUpdateFormulas') {
            updateInternalDataFormulas();
        }
        if (e.target && e.target.id === 'internalUpdateDealList') {
            updateInternalDataDealList();
        }
        if (e.target && e.target.id === 'internalSaveDealList') {
            saveInternalDataDealList();
        }
    });

    // Deal list text change
    document.addEventListener('input', function(e) {
        if (e.target && e.target.id === 'internalDealListText') {
            updateInternalDealCount();
        }
        if (e.target && e.target.id === 'internalGlobalId') {
            parseInternalGlobalId();
        }
    });

    // ===== INTERNAL DATA FUNCTIONS =====

    function handleInternalSheetTypeChange() {
        const customRow = document.getElementById('internalCustomLinkRow');
        const customRadio = document.getElementById('internalCustom');

        if (customRadio.checked) {
            customRow.style.display = 'block';
        } else {
            customRow.style.display = 'none';
            // Auto load sheets for Type 1 or Type 2
            loadInternalDataSheets();
        }
    }

    function handleInternalCopyModeChange() {
        const autoRow = document.getElementById('internalAutoRow');
        const manualRow = document.getElementById('internalManualRow');
        const resultRow = document.getElementById('internalResultRow');
        const autoRadio = document.getElementById('internalAutoCopy');

        if (autoRadio.checked) {
            autoRow.style.display = 'block';
            manualRow.style.display = 'none';
            resultRow.style.display = 'block';
        } else {
            autoRow.style.display = 'none';
            manualRow.style.display = 'block';
            resultRow.style.display = 'none';
        }
    }

    function loadInternalDataSheets() {
        const type1Radio = document.getElementById('internalType1');
        const type2Radio = document.getElementById('internalType2');
        const customRadio = document.getElementById('internalCustom');
        const customLink = document.getElementById('internalCustomLink');

        let sheetType = 'type1';
        let customId = '';

        if (type1Radio.checked) {
            sheetType = 'type1';
        } else if (type2Radio.checked) {
            sheetType = 'type2';
        } else if (customRadio.checked) {
            sheetType = 'custom';
            customId = customLink.value.trim();
            if (!customId) {
                showAlert('Vui lòng nhập Custom Spreadsheet ID', 'warning');
                return;
            }
        }

        callApi('internal_data/get_sheets', {
            sheet_type: sheetType,
            custom_id: customId
        })
        .then(data => {
            if (data.success) {
                internalDataSourceId = data.spreadsheet_id;
                internalDataSheets = data.sheets;
                displayInternalDataSheets(data.sheets);
                showAlert('Đã load sheets thành công', 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading internal data sheets:', error);
            showAlert('Lỗi kết nối khi load sheets', 'danger');
        });
    }

    function displayInternalDataSheets(sheets) {
        const container = document.getElementById('internalSheetsList');
        container.innerHTML = '';

        const sheetNames = Object.keys(sheets);
        const numCols = 2;
        const numRows = Math.ceil(sheetNames.length / numCols);

        for (let col = 0; col < numCols; col++) {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6';

            for (let row = 0; row < numRows; row++) {
                const idx = row + col * numRows;
                if (idx < sheetNames.length) {
                    const sheetName = sheetNames[idx];
                    const sheetId = sheets[sheetName];

                    const checkDiv = document.createElement('div');
                    checkDiv.className = 'form-check';
                    checkDiv.innerHTML = `
                        <input class="form-check-input internal-sheet-checkbox" type="checkbox"
                               value="${sheetId}" id="internal_sheet_${idx}" checked>
                        <label class="form-check-label" for="internal_sheet_${idx}">
                            ${sheetName}
                        </label>
                    `;
                    colDiv.appendChild(checkDiv);
                }
            }
            container.appendChild(colDiv);
        }
    }

    function toggleAllInternalSheets() {
        const checkboxes = document.querySelectorAll('.internal-sheet-checkbox');
        const toggleBtn = document.getElementById('internalToggleAll');

        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
        });

        toggleBtn.textContent = allChecked ? 'Check All' : 'Uncheck All';
    }

    function startInternalDataCopy() {
        const checkboxes = document.querySelectorAll('.internal-sheet-checkbox:checked');
        const selectedSheets = Array.from(checkboxes).map(cb => parseInt(cb.value));

        if (selectedSheets.length === 0) {
            showAlert('Vui lòng chọn ít nhất một sheet để copy', 'warning');
            return;
        }

        const autoRadio = document.getElementById('internalAutoCopy');
        const copyMode = autoRadio.checked ? 'auto' : 'manual';

        let destinationData = {};

        if (copyMode === 'auto') {
            const title = document.getElementById('internalAutoTitle').value.trim();
            if (!title) {
                showAlert('Vui lòng nhập tên cho spreadsheet mới', 'warning');
                return;
            }
            destinationData.title = title;
        } else {
            const destId = document.getElementById('internalManualDest').value.trim();
            if (!destId) {
                showAlert('Vui lòng nhập Spreadsheet ID đích', 'warning');
                return;
            }
            destinationData.destination_id = destId;
        }

        // Show progress
        showInternalDataProgress(true);
        document.getElementById('internalCopyBtn').disabled = true;

        callApi('internal_data/copy_sheets', {
            source_id: internalDataSourceId,
            selected_sheets: selectedSheets,
            copy_mode: copyMode,
            destination_data: destinationData
        })
        .then(data => {
            if (data.success) {
                internalDataCurrentTaskId = data.task_id;
                startInternalDataStatusCheck();
                showAlert('Đã bắt đầu copy sheets', 'info');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
                showInternalDataProgress(false);
                document.getElementById('internalCopyBtn').disabled = false;
            }
        })
        .catch(error => {
            console.error('Error copying sheets:', error);
            showAlert('Lỗi kết nối khi copy sheets', 'danger');
            showInternalDataProgress(false);
            document.getElementById('internalCopyBtn').disabled = false;
        });
    }

    function showInternalDataProgress(show) {
        const progressDiv = document.getElementById('internalProgress');
        if (show) {
            progressDiv.style.display = 'block';
            document.getElementById('internalProgressBar').style.width = '0%';
            document.getElementById('internalStatus').textContent = 'Đang chuẩn bị...';
            document.getElementById('internalLog').innerHTML = '';
        } else {
            progressDiv.style.display = 'none';
        }
    }

    function startInternalDataStatusCheck() {
        if (!internalDataCurrentTaskId) return;

        const checkStatus = () => {
            fetch(`/api/get_task_status/${internalDataCurrentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateInternalDataStatus(data.task);

                        if (data.task.status === 'completed' || data.task.status === 'error') {
                            document.getElementById('internalCopyBtn').disabled = false;
                            if (data.task.status === 'completed' && data.task.result_url) {
                                document.getElementById('internalResultLink').value = data.task.result_url;
                            }
                        } else {
                            setTimeout(checkStatus, 1000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking status:', error);
                    document.getElementById('internalCopyBtn').disabled = false;
                });
        };

        checkStatus();
    }

    function updateInternalDataStatus(task) {
        const progressBar = document.getElementById('internalProgressBar');
        const status = document.getElementById('internalStatus');
        const log = document.getElementById('internalLog');

        // Update progress (simple progress based on status)
        let progress = 0;
        if (task.status === 'running') progress = 50;
        else if (task.status === 'completed') progress = 100;
        else if (task.status === 'error') progress = 0;

        progressBar.style.width = `${progress}%`;
        status.textContent = task.status === 'completed' ? 'Hoàn thành' :
                           task.status === 'error' ? 'Lỗi' : 'Đang xử lý...';

        // Update log
        if (task.log_messages && task.log_messages.length > 0) {
            log.innerHTML = task.log_messages.map(msg => `<div>${msg}</div>`).join('');
            log.scrollTop = log.scrollHeight;
        }
    }

    function getInternalDataFormulas() {
        const globalId = document.getElementById('internalGlobalId').value.trim();
        const globalSheet = document.getElementById('internalGlobalSheet').value.trim();

        if (!globalId || !globalSheet) {
            showAlert('Vui lòng nhập External Spreadsheet ID và tên sheet', 'warning');
            return;
        }

        // Fill other fields with same data
        document.getElementById('internalClusterId').value = globalId;
        document.getElementById('internalClusterSheet').value = globalSheet;
        document.getElementById('internalSourceId').value = globalId;
        document.getElementById('internalSourceSheet').value = globalSheet;
        document.getElementById('internalShortenId').value = globalId;
        document.getElementById('internalShortenSheet').value = globalSheet;

        showAlert('Đã cập nhật dữ liệu cho tất cả các trường', 'success');
    }

    function parseInternalGlobalId() {
        const input = document.getElementById('internalGlobalId');
        const text = input.value.trim();

        // Parse URL to ID if needed
        const match = text.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
        if (match) {
            input.value = match[1];
        }
    }

    function updateInternalDataFormulas() {
        const resultLink = document.getElementById('internalResultLink').value.trim();
        if (!resultLink) {
            showAlert('Vui lòng tạo bản sao trước khi cập nhật formulas', 'warning');
            return;
        }

        // Extract spreadsheet ID from result link
        const match = resultLink.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
        if (!match) {
            showAlert('Không thể lấy Spreadsheet ID từ link kết quả', 'danger');
            return;
        }

        const spreadsheetId = match[1];

        const formulasData = {
            cluster_id: document.getElementById('internalClusterId').value.trim(),
            cluster_sheet: document.getElementById('internalClusterSheet').value.trim(),
            source_id: document.getElementById('internalSourceId').value.trim(),
            source_sheet: document.getElementById('internalSourceSheet').value.trim(),
            shorten_id: document.getElementById('internalShortenId').value.trim(),
            shorten_sheet: document.getElementById('internalShortenSheet').value.trim()
        };

        callApi('internal_data/update_formulas', {
            spreadsheet_id: spreadsheetId,
            formulas_data: formulasData
        })
        .then(data => {
            if (data.success) {
                showAlert('Cập nhật formulas thành công', 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating formulas:', error);
            showAlert('Lỗi kết nối khi cập nhật formulas', 'danger');
        });
    }

    function updateInternalDealCount() {
        const text = document.getElementById('internalDealListText').value.trim();
        const lines = text.split('\n').filter(line => line.trim());
        const uniqueIds = [...new Set(lines.map(line => line.trim()))];

        document.getElementById('internalDealListCount').textContent = `Số ID: ${uniqueIds.length}`;
    }

    function saveInternalDataDealList() {
        const text = document.getElementById('internalDealListText').value.trim();
        const lines = text.split('\n').filter(line => line.trim());
        internalDataDealIds = [...new Set(lines.map(line => line.trim()))];

        document.getElementById('internalDealCount').textContent = `Tổng Product ID: ${internalDataDealIds.length}`;

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('internalDealListModal'));
        modal.hide();

        showAlert(`Đã lưu ${internalDataDealIds.length} Product IDs`, 'success');
    }

    function updateInternalDataDealList() {
        const resultLink = document.getElementById('internalResultLink').value.trim();
        if (!resultLink) {
            showAlert('Vui lòng tạo bản sao trước khi cập nhật Deal list', 'warning');
            return;
        }

        if (internalDataDealIds.length === 0) {
            showAlert('Vui lòng nhập danh sách Product IDs trước', 'warning');
            return;
        }

        // Extract spreadsheet ID from result link
        const match = resultLink.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
        if (!match) {
            showAlert('Không thể lấy Spreadsheet ID từ link kết quả', 'danger');
            return;
        }

        const spreadsheetId = match[1];

        callApi('internal_data/update_deal_list', {
            spreadsheet_id: spreadsheetId,
            deal_ids: internalDataDealIds
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
            } else {
                showAlert('Lỗi: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating deal list:', error);
            showAlert('Lỗi kết nối khi cập nhật Deal list', 'danger');
        });
    }

    // Tab activation event listeners
    document.getElementById('data-handler-tab').addEventListener('click', function() {
        // Auto-load data nếu chưa có
        setTimeout(() => {
            if (!rawDataSourceSpreadsheetId) {
                loadRawDataSheets();
            }
        }, 100);
    });

    document.getElementById('internal-data-tab').addEventListener('click', function() {
        // Auto-load sheets nếu chưa có
        setTimeout(() => {
            if (!internalDataSourceId) {
                loadInternalDataSheets();
            }
        }, 100);
    });

    // Auto-load Internal Data sheets khi trang được tải (vì nó là tab active đầu tiên)
    setTimeout(() => {
        // Auto load Type 1 sheets for Internal Data
        if (typeof loadInternalDataSheets === 'function') {
            loadInternalDataSheets();
        }
    }, 1000);

});

// API helper function
async function callApi(endpoint, data = {}) {
    try {
        const response = await fetch(`/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Lỗi khi gọi API:', error);
        throw error;
    }
}