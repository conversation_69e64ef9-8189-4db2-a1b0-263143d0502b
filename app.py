from flask import Flask, render_template, request, jsonify, send_file
import os
import uuid
import threading
import time
from datetime import datetime
import subprocess
import platform
import webbrowser

# Import các module chương trình
try:
    import data_scrape_core as shopee_core
except ImportError:
    shopee_core = None
    print("Warning: data_scrape_core not found")

try:
    from data_handler_core import DataHandlerCore
except ImportError:
    DataHandlerCore = None
    print("Warning: data_handler_core not found")

try:
    from gsheet_manager import GoogleSheetManager
except ImportError:
    GoogleSheetManager = None
    print("Warning: gsheet_manager not found")

app = Flask(__name__)

# Thông tin đăng nhập mặc định
DEFAULT_USERNAME = "princekiix"
DEFAULT_PASSWORD = "Beyondk@2025"

# Lưu trữ thông tin các tác vụ đang chạy
tasks = {}
task_lock = threading.Lock()

# Internal Data Configuration
INTERNAL_DATA_CONFIG = {
    'DEFAULT_SHEET_ID_TYPE1': "1DKDP_fjTT4e9ubsENiGP7hnLFOufnLz-l_R9b3K_tns",
    'DEFAULT_SHEET_ID_TYPE2': "1uFuEn4CbcsmfhqS_CWapyOvpMlpcnER2sTlVQ4lmCCo",
    'CREDENTIALS_BASE64': "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    'ALTERNATIVE_CREDENTIALS': [
        "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    ]
}

# Đường dẫn lưu trữ tạm thời
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
os.makedirs(TEMP_DIR, exist_ok=True)

# Danh sách các thư mục thông dụng
COMMON_FOLDERS = {
    'temp': TEMP_DIR,
    'desktop': os.path.join(os.path.expanduser('~'), 'Desktop'),
    'documents': os.path.join(os.path.expanduser('~'), 'Documents'),
    'downloads': os.path.join(os.path.expanduser('~'), 'Downloads')
}

# Danh sách ổ đĩa (chỉ cho Windows)
def get_drives():
    if platform.system() == 'Windows':
        drives = []
        for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                drives.append(drive)
        return drives
    return []

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    try:
        # Gọi hàm đăng nhập từ core để lấy cookies
        if shopee_core:
            shopee_core.get_logged_in_cookies(username, password, headless=True)
            # Kiểm tra xem headers có được tạo không
            headers = shopee_core.get_auth_headers()
        else:
            return jsonify({'success': False, 'message': 'Shopee core module không khả dụng'})
        if headers:
            return jsonify({'success': True, 'message': 'Đăng nhập thành công'})
        else:
            return jsonify({'success': False, 'message': 'Đăng nhập thất bại: Không nhận được headers'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Đăng nhập thất bại: {str(e)}'})

@app.route('/api/list_folders', methods=['POST'])
def list_folders():
    data = request.json
    path = data.get('path', '')

    # Nếu đường dẫn trống, trả về danh sách ổ đĩa (Windows) hoặc thư mục gốc (Unix)
    if not path:
        if platform.system() == 'Windows':
            drives = get_drives()
            return jsonify({
                'success': True,
                'current_path': '',
                'parent_path': None,
                'folders': [{'name': drive, 'path': drive} for drive in drives]
            })
        else:
            path = '/'

    # Xử lý đường dẫn để đảm bảo tính bảo mật
    try:
        path = process_output_path(path)

        # Kiểm tra nếu đường dẫn không tồn tại
        if not os.path.exists(path):
            return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

        # Lấy đường dẫn cha
        parent_path = os.path.dirname(path)
        if path == parent_path:  # Trường hợp ở thư mục gốc
            parent_path = None

        # Danh sách các thư mục con
        folders = []
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                folders.append({
                    'name': item,
                    'path': item_path
                })

        # Sắp xếp theo tên
        folders.sort(key=lambda x: x['name'].lower())

        return jsonify({
            'success': True,
            'current_path': path,
            'parent_path': parent_path,
            'folders': folders
        })

    except PermissionError:
        return jsonify({'success': False, 'message': 'Không có quyền truy cập thư mục này'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/check_path', methods=['POST'])
def check_path():
    data = request.json
    path = data.get('path', '')

    if not path:
        return jsonify({'success': False, 'message': 'Đường dẫn không được để trống'})

    try:
        # Xử lý đường dẫn để đảm bảo tính bảo mật
        path = process_output_path(path)

        # Kiểm tra đường dẫn có tồn tại không
        if os.path.exists(path):
            if os.path.isdir(path):
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                try:
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    return jsonify({'success': True, 'message': 'Đường dẫn hợp lệ và có quyền ghi', 'path': path, 'exists': True, 'writable': True})
                except:
                    return jsonify({'success': True, 'message': 'Đường dẫn tồn tại nhưng không có quyền ghi', 'path': path, 'exists': True, 'writable': False})
            else:
                return jsonify({'success': False, 'message': 'Đường dẫn là một file, không phải thư mục'})
        else:
            # Thử tạo thư mục
            try:
                os.makedirs(path, exist_ok=True)
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return jsonify({'success': True, 'message': 'Đã tạo thư mục thành công', 'path': path, 'exists': True, 'writable': True})
            except:
                return jsonify({'success': False, 'message': 'Không thể tạo thư mục'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/open_folder', methods=['POST'])
def open_folder():
    data = request.json
    path = data.get('path', '')

    if not path or not os.path.exists(path):
        return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

    try:
        if platform.system() == "Windows":
            os.startfile(path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.call(["open", path])
        else:  # Linux
            subprocess.call(["xdg-open", path])
        return jsonify({'success': True, 'message': 'Đã mở thư mục'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể mở thư mục: {str(e)}'})

@app.route('/api/create_folder', methods=['POST'])
def create_folder():
    data = request.json
    parent_path = data.get('parent_path', '')
    folder_name = data.get('folder_name', '')

    if not parent_path or not folder_name:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp đường dẫn cha và tên thư mục'})

    # Xử lý tên thư mục để đảm bảo an toàn
    folder_name = folder_name.strip()
    if not folder_name or '/' in folder_name or '\\' in folder_name or folder_name in ['.', '..']:
        return jsonify({'success': False, 'message': 'Tên thư mục không hợp lệ'})

    try:
        # Xử lý đường dẫn cha
        parent_path = process_output_path(parent_path)

        # Kiểm tra đường dẫn cha có tồn tại không
        if not os.path.exists(parent_path) or not os.path.isdir(parent_path):
            return jsonify({'success': False, 'message': 'Đường dẫn cha không tồn tại'})

        # Tạo đường dẫn thư mục mới
        new_folder_path = os.path.join(parent_path, folder_name)

        # Kiểm tra thư mục đã tồn tại chưa
        if os.path.exists(new_folder_path):
            return jsonify({'success': False, 'message': 'Thư mục đã tồn tại'})

        # Tạo thư mục
        os.makedirs(new_folder_path, exist_ok=True)

        return jsonify({
            'success': True,
            'message': f'Đã tạo thư mục "{folder_name}" thành công',
            'new_folder_path': new_folder_path
        })

    except PermissionError:
        return jsonify({'success': False, 'message': 'Không có quyền tạo thư mục tại vị trí này'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi tạo thư mục: {str(e)}'})

@app.route('/api/start_scraping', methods=['POST'])
def start_scraping():
    data = request.json
    shop_ids = data.get('shop_ids', [])
    output_path = data.get('output_path', '')
    output_filename = data.get('output_filename', f'scraped_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    if not shop_ids:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp ít nhất một ID shop'})

    # Tạo task ID và lưu trữ thông tin
    task_id = str(uuid.uuid4())

    # Xử lý đường dẫn lưu trữ
    if not output_path:
        # Nếu không chọn đường dẫn, sử dụng thư mục temp
        output_path = TEMP_DIR
    else:
        output_path = process_output_path(output_path)

    # Đảm bảo output_path tồn tại
    try:
        os.makedirs(output_path, exist_ok=True)
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể tạo thư mục: {str(e)}'})

    # Đường dẫn đầy đủ cho file output
    full_output_path = os.path.join(output_path, output_filename)
    if not full_output_path.endswith('.xlsx'):
        full_output_path += '.xlsx'

    # Khởi tạo thông tin tác vụ
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'shop_ids': shop_ids,
            'total_shops': len(shop_ids),
            'completed_shops': 0,
            'total_products': 0,
            'output_file': full_output_path,
            'output_folder': output_path,
            'log_messages': [],
            'shop_statuses': {shop_id: {'status': 'waiting', 'product_count': 0} for shop_id in shop_ids}
        }

    # Bắt đầu thread để xử lý dữ liệu
    thread = threading.Thread(
        target=process_data,
        args=(task_id, username, password, shop_ids, output_path, output_filename)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': f'Đã bắt đầu xử lý {len(shop_ids)} shop'
    })

def process_output_path(path):
    """Xử lý đường dẫn đầu ra được chọn từ giao diện"""
    # Nếu là thư mục trong danh sách thông dụng
    if path.lower() in COMMON_FOLDERS:
        return COMMON_FOLDERS[path.lower()]

    # Nếu là đường dẫn tương đối, chuyển thành đường dẫn tuyệt đối
    if not os.path.isabs(path):
        path = os.path.join(os.path.dirname(os.path.abspath(__file__)), path)

    # Mở rộng ~ nếu có (home directory)
    path = os.path.expanduser(path)

    # Đảm bảo đường dẫn hợp lệ
    if os.name == 'nt':  # Windows
        # Xử lý ổ đĩa cho Windows
        if path.startswith('/') and len(path) > 2 and path[2] == ':':
            # Chuyển /C:/path thành C:/path
            path = path[1:]
        elif not path[1] == ':':
            # Nếu không có chỉ định ổ đĩa, thêm ổ đĩa hiện tại
            current_drive = os.getcwd()[:2]
            if not path.startswith('/'):
                path = os.path.join(current_drive, path)
            else:
                path = current_drive + path

    # Xử lý các dấu cách đường dẫn
    path = os.path.normpath(path)

    return path

def process_data(task_id, username, password, shop_ids, output_path, output_filename):
    """Hàm xử lý dữ liệu trong thread riêng biệt"""

    def log_callback(message):
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['log_messages'].append(message)

                # Phân tích log message để xác định shop_id và cập nhật trạng thái
                import re
                shop_id_match = re.search(r"Đang lấy dữ liệu từ shop ID: (\d+)", message)
                completed_match = re.search(r"Shop ID (\d+): Hoàn thành với (\d+) sản phẩm", message)
                no_products_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào", message)
                no_products_retry_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào \(đã retry (\d+) lần\)", message)
                error_match = re.search(r"Lỗi khi xử lý shop ID (\d+)", message)

                if shop_id_match:
                    shop_id = shop_id_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'processing'
                elif completed_match:
                    shop_id = completed_match.group(1)
                    product_count = int(completed_match.group(2))
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'completed'
                    tasks[task_id]['shop_statuses'][shop_id]['product_count'] = product_count
                    tasks[task_id]['completed_shops'] += 1
                    tasks[task_id]['total_products'] += product_count
                elif no_products_match or no_products_retry_match:
                    if no_products_match:
                        shop_id = no_products_match.group(1)
                    else:
                        shop_id = no_products_retry_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'no_products'
                    tasks[task_id]['completed_shops'] += 1
                elif error_match:
                    shop_id = error_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'error'
                    tasks[task_id]['completed_shops'] += 1

    try:
        full_output_path = os.path.join(output_path, output_filename)
        if not full_output_path.endswith('.xlsx'):
            full_output_path += '.xlsx'

        # Gọi hàm xử lý từ core
        if shopee_core:
            result = shopee_core.fetch_and_save_multiple_shops(
            username,
            password,
            shop_ids,
            output_filename=full_output_path,
            headless=True,
            timeout=30,
            max_retries=3,
                log_callback=log_callback,
                max_workers=8  # Sử dụng 8 luồng đồng thời như trong PyQt app
            )
        else:
            result = False

        # Cập nhật trạng thái tác vụ
        with task_lock:
            if task_id in tasks:
                if result:
                    tasks[task_id]['status'] = 'completed'
                    tasks[task_id]['log_messages'].append(f"📊 Tổng cộng: {tasks[task_id]['total_products']} sản phẩm được lưu vào file Excel")
                else:
                    tasks[task_id]['status'] = 'error'
                    tasks[task_id]['log_messages'].append("❌ Xử lý dữ liệu thất bại")

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_task_status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    with task_lock:
        if task_id not in tasks:
            return jsonify({'success': False, 'message': 'Không tìm thấy tác vụ'})

        task = tasks[task_id].copy()

        # Tính thời gian đã chạy
        elapsed_time = time.time() - task['start_time']
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        task['elapsed_time'] = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

        # Tính phần trăm hoàn thành
        if task['total_shops'] > 0:
            task['progress_percent'] = int((task['completed_shops'] / task['total_shops']) * 100)
        else:
            task['progress_percent'] = 0

        return jsonify({'success': True, 'task': task})

@app.route('/api/get_file/<task_id>', methods=['GET'])
def get_file(task_id):
    with task_lock:
        if task_id not in tasks or 'output_file' not in tasks[task_id]:
            return jsonify({'success': False, 'message': 'Không tìm thấy file'})

        output_file = tasks[task_id]['output_file']

    if not os.path.exists(output_file):
        return jsonify({'success': False, 'message': 'File không tồn tại'})

    return send_file(output_file, as_attachment=True)

@app.route('/api/convert_csv', methods=['POST'])
def convert_csv():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'Không tìm thấy file'})

    csv_file = request.files['file']

    if csv_file.filename == '':
        return jsonify({'success': False, 'message': 'Không có file nào được chọn'})

    if not csv_file.filename.endswith('.csv'):
        return jsonify({'success': False, 'message': 'Chỉ chấp nhận file CSV'})

    # Tạo đường dẫn lưu file tạm thời
    csv_temp_path = os.path.join(TEMP_DIR, f"temp_{uuid.uuid4()}.csv")
    excel_temp_path = csv_temp_path.replace('.csv', '.xlsx')

    # Lưu file CSV
    csv_file.save(csv_temp_path)

    # Tạo task ID
    task_id = str(uuid.uuid4())
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'output_file': excel_temp_path,
            'output_folder': os.path.dirname(excel_temp_path),
            'log_messages': ["🔄 Bắt đầu chuyển đổi CSV thành Excel..."]
        }

    # Bắt đầu thread để xử lý chuyển đổi
    thread = threading.Thread(
        target=process_csv_conversion,
        args=(task_id, csv_temp_path, excel_temp_path)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': 'Đã bắt đầu chuyển đổi CSV thành Excel'
    })

def process_csv_conversion(task_id, csv_path, excel_path):
    """Hàm xử lý chuyển đổi CSV thành Excel trong thread riêng biệt"""
    try:
        import pandas as pd
        from openpyxl import Workbook
        from openpyxl.styles import Font

        # Đọc CSV
        df = pd.read_csv(csv_path)

        # Format theo yêu cầu
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        new_df = pd.DataFrame({
            '': key_col,
            ' ': sku_col,
            '  ': id_col
        })

        # Thêm các cột còn lại
        column_mapping = {
            'linkProduct': 'Link sản phẩm',
            'linkShop': 'Link Shop',
            'name': 'Tên sản phẩm',
            'brand': 'Thương hiệu',
            'description': 'Mô tả',
            'timeCreate': 'Ngày tạo',
            'itemID': 'Mã Shop',
            'shopID': 'Mã Sản phẩm',
            'categoryMain': 'Chuyên mục',
            'categoryTree': 'Chuyên mục.1',
            'price': 'Giá hiện tại',
            'priceMin': 'Giá thấp nhất',
            'priceMax': 'Giá cao nhất',
            'discount': 'Giảm giá',
            'stock': 'Tồn kho',
            'weight': 'Cân nặng',
            'image': 'Hình ảnh',
            'cmtCount': 'Số Đánh giá',
            'viewCount': 'Số lượt xem',
            'likedCount': 'Số thích',
            'itemRating': 'Điểm đánh giá',
            'sold_30day': 'Đã bán 30 ngày',
            'sale_30day': 'Doanh số 30 ngày',
            'sold_alltime': 'Đã bán toàn thời gian',
            'sale_alltime': 'Doanh số toàn thời gian',
            'location': 'Vị trí',
            'video': 'Video'
        }

        for col_name, new_name in column_mapping.items():
            if col_name in df.columns:
                new_df[new_name] = df[col_name]

        # Tạo Excel
        wb = Workbook()
        ws = wb.active

        # Ghi headers
        for c_idx, column in enumerate(new_df.columns, 1):
            if c_idx <= 3:
                header_value = ""  # 3 cột đầu trống
            else:
                header_value = column
            ws.cell(row=1, column=c_idx, value=header_value)
            ws.cell(row=1, column=c_idx).font = Font(bold=False)

        # Ghi dữ liệu từ row 2
        for r_idx, (_, row) in enumerate(new_df.iterrows(), 2):
            for c_idx, value in enumerate(row, 1):
                try:
                    ws.cell(row=r_idx, column=c_idx, value=value)
                except:
                    ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

        # Lưu file Excel
        wb.save(excel_path)

        # Cập nhật trạng thái
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'completed'
                tasks[task_id]['log_messages'].append("✅ Chuyển đổi thành công!")
                tasks[task_id]['log_messages'].append(f"📊 Đã xử lý {len(df)} dòng dữ liệu")

        # Xóa file CSV tạm thời
        try:
            os.remove(csv_path)
        except:
            pass

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_default_credentials', methods=['GET'])
def get_default_credentials():
    return jsonify({
        'username': DEFAULT_USERNAME,
        'password': DEFAULT_PASSWORD
    })

@app.route('/api/update_credentials', methods=['POST'])
def update_credentials():
    data = request.json
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'success': False, 'message': 'Username và password không được để trống'})

    global DEFAULT_USERNAME, DEFAULT_PASSWORD
    DEFAULT_USERNAME = username
    DEFAULT_PASSWORD = password

    # Cập nhật thông tin đăng nhập và force refresh
    try:
        if shopee_core:
            shopee_core.get_logged_in_cookies(username, password, headless=True, force_refresh=True)
            return jsonify({'success': True, 'message': 'Đã cập nhật thông tin đăng nhập'})
        else:
            return jsonify({'success': False, 'message': 'Shopee core module không khả dụng'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật thông tin đăng nhập: {str(e)}'})

# ===== RAW DATA PROCESSING ENDPOINTS =====

@app.route('/api/raw_data/get_sheets', methods=['POST'])
def raw_data_get_sheets():
    """Lấy danh sách sheets từ Google Spreadsheet"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_url = data.get('spreadsheet_url', '')

    if not spreadsheet_url:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp URL spreadsheet'})

    try:
        print(f"🔍 Đang xử lý URL: {spreadsheet_url}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Extract spreadsheet ID from URL
        spreadsheet_id = data_handler.extract_spreadsheet_id(spreadsheet_url)
        print(f"📋 Spreadsheet ID: {spreadsheet_id}")

        if not spreadsheet_id:
            return jsonify({'success': False, 'message': 'URL spreadsheet không hợp lệ'})

        # Get sheets list
        sheet_list = data_handler.get_sheet_list(spreadsheet_id)
        print(f"📄 Tìm thấy {len(sheet_list)} sheets: {sheet_list}")

        return jsonify({
            'success': True,
            'spreadsheet_id': spreadsheet_id,
            'sheets': sheet_list
        })

    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi lấy danh sách sheets: {str(e)}'})

@app.route('/api/raw_data/import', methods=['POST'])
def raw_data_import():
    """Import dữ liệu từ sheets nguồn sang sheet đích"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    source_spreadsheet_id = data.get('source_spreadsheet_id', '')
    target_spreadsheet_id = data.get('target_spreadsheet_id', '')
    selected_sheets = data.get('selected_sheets', [])
    target_sheet = data.get('target_sheet', 'Pool Deal')
    mode = data.get('mode', 'copy')

    if not source_spreadsheet_id or not selected_sheets:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp thông tin nguồn và chọn ít nhất một sheet'})

    if not target_spreadsheet_id:
        target_spreadsheet_id = source_spreadsheet_id

    try:
        print(f"🔄 Bắt đầu import dữ liệu với chế độ '{mode}'")
        print(f"📊 Source: {source_spreadsheet_id}")
        print(f"🎯 Target: {target_spreadsheet_id}")
        print(f"📁 Sheets: {selected_sheets}")
        print(f"📋 Target sheet: {target_sheet}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Chuẩn bị source data theo format mới
        source_data = []
        for sheet_name in selected_sheets:
            source_data.append({
                'spreadsheet_id': source_spreadsheet_id,
                'sheet_name': sheet_name,
                'start_row': 3,  # Bắt đầu từ dòng 3 (bỏ qua header)
                'end_row': None  # Lấy đến hết
            })

        # Tạo column mapping tự động từ sheet đích
        column_mapping = data_handler.create_column_mapping_from_target_sheet(
            target_spreadsheet_id,
            target_sheet,
            data_handler.remove_vietnamese_accents
        )

        # Thực hiện import dữ liệu
        result = data_handler.process_data_import(
            target_spreadsheet_id,
            source_data,
            target_sheet,
            column_mapping,
            data_handler.remove_vietnamese_accents,
            mode
        )

        if result:
            return jsonify({
                'success': True,
                'message': f'Import dữ liệu thành công với chế độ {mode}'
            })
        else:
            return jsonify({'success': False, 'message': 'Import dữ liệu thất bại'})

    except Exception as e:
        print(f"❌ Lỗi import: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi import dữ liệu: {str(e)}'})

@app.route('/api/raw_data/process', methods=['POST'])
def raw_data_process():
    """Xử lý dữ liệu đã import"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    sheet_name = data.get('sheet_name', 'Pool Deal')
    rules = data.get('rules', [])

    if not spreadsheet_id:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp ID spreadsheet'})

    try:
        print(f"🔧 Bắt đầu xử lý dữ liệu trong sheet '{sheet_name}'")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # KHÔNG sử dụng rules mặc định - chỉ xử lý khi có rules từ user
        if not rules:
            return jsonify({'success': False, 'message': 'Không có quy tắc nào được cung cấp'})

        # Process data with rules
        result = data_handler.process_imported_data_with_rules(spreadsheet_id, sheet_name, rules)

        if result:
            return jsonify({
                'success': True,
                'message': 'Xử lý dữ liệu thành công'
            })
        else:
            return jsonify({'success': False, 'message': 'Xử lý dữ liệu thất bại'})

    except Exception as e:
        print(f"❌ Lỗi xử lý: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi xử lý dữ liệu: {str(e)}'})

@app.route('/api/raw_data/get_headers', methods=['POST'])
def raw_data_get_headers():
    """Lấy danh sách headers từ sheet cụ thể"""
    if not DataHandlerCore:
        return jsonify({'success': False, 'message': 'Data Handler Core module không khả dụng'})

    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    sheet_name = data.get('sheet_name', '')

    if not spreadsheet_id or not sheet_name:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp spreadsheet ID và tên sheet'})

    try:
        print(f"🔍 Lấy headers từ sheet '{sheet_name}' trong spreadsheet: {spreadsheet_id}")

        # Initialize DataHandlerCore
        data_handler = DataHandlerCore()

        # Lấy headers từ dòng 3 (như Desktop App)
        headers = data_handler.get_sheet_headers(spreadsheet_id, sheet_name, header_row=3)

        if headers:
            # Sử dụng hàm filter chung để đảm bảo consistency
            filtered_headers, _ = data_handler.filter_headers_for_processing(headers)

            print(f"📋 Đã lấy {len(filtered_headers)} headers sau khi filter")

            return jsonify({
                'success': True,
                'headers': filtered_headers,
                'total_headers': len(headers),
                'filtered_headers': len(filtered_headers)
            })
        else:
            return jsonify({'success': False, 'message': 'Không tìm thấy headers trong sheet này'})

    except Exception as e:
        print(f"❌ Lỗi khi lấy headers: {str(e)}")
        return jsonify({'success': False, 'message': f'Lỗi khi lấy headers: {str(e)}'})

# ===== INTERNAL DATA ENDPOINTS =====

def get_internal_data_manager():
    """Khởi tạo Google Sheet Manager cho Internal Data"""
    if not GoogleSheetManager:
        return None
    try:
        return GoogleSheetManager(
            auth_type='oauth',
            credentials_data=INTERNAL_DATA_CONFIG['CREDENTIALS_BASE64'],
            all_oauth_credentials=INTERNAL_DATA_CONFIG['ALTERNATIVE_CREDENTIALS']
        )
    except Exception as e:
        print(f"❌ Lỗi khởi tạo Internal Data Manager: {str(e)}")
        return None

def force_recalculate_sheets(sheets_service, spreadsheet_id):
    """Force Google Sheets recalculate formulas"""
    requests = [
        {"addSheet": {"properties": {"title": "TempSheet"}}},
        {"deleteSheet": {"sheetId": None}}
    ]
    try:
        sheets_service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body={"requests": requests}
        ).execute()
    except Exception as e:
        print(f"Lỗi khi thực hiện batchUpdate: {str(e)}")

@app.route('/api/internal_data/get_sheets', methods=['POST'])
def internal_data_get_sheets():
    """Lấy danh sách sheets từ template spreadsheet"""
    data = request.json
    sheet_type = data.get('sheet_type', 'type1')  # type1, type2, custom
    custom_id = data.get('custom_id', '')

    gsheet_manager = get_internal_data_manager()
    if not gsheet_manager:
        return jsonify({'success': False, 'message': 'Google Sheets Manager không khả dụng'})

    try:
        # Xác định spreadsheet ID
        if sheet_type == 'type1':
            spreadsheet_id = INTERNAL_DATA_CONFIG['DEFAULT_SHEET_ID_TYPE1']
        elif sheet_type == 'type2':
            spreadsheet_id = INTERNAL_DATA_CONFIG['DEFAULT_SHEET_ID_TYPE2']
        else:  # custom
            if not custom_id:
                return jsonify({'success': False, 'message': 'Vui lòng cung cấp Custom Spreadsheet ID'})
            # Parse URL nếu cần
            import re
            match = re.search(r'/d/([a-zA-Z0-9-_]+)/', custom_id)
            spreadsheet_id = match.group(1) if match else custom_id.strip()

        sheets_service = gsheet_manager.get_sheets_service()
        result = sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()

        sheets_dict = {
            sheet['properties']['title']: sheet['properties']['sheetId']
            for sheet in result.get('sheets', [])
        }

        return jsonify({
            'success': True,
            'spreadsheet_id': spreadsheet_id,
            'sheets': sheets_dict
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi lấy danh sách sheets: {str(e)}'})

def process_copy_sheets(task_id, gsheet_manager, source_id, selected_sheets, copy_mode, destination_data):
    """Xử lý copy sheets trong background thread"""
    def log_callback(message):
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['log_messages'].append(message)

    try:
        sheets_service = gsheet_manager.get_sheets_service()

        if copy_mode == 'auto':
            # Tạo spreadsheet mới
            new_title = destination_data.get('title', 'New Spreadsheet')
            log_callback("Đang tạo spreadsheet mới...")

            new_spreadsheet = sheets_service.spreadsheets().create(
                body={'properties': {'title': new_title, 'locale': 'en_US'}}
            ).execute()
            new_id = new_spreadsheet['spreadsheetId']

            # Copy sheets
            log_callback("Đang copy sheets...")
            total_sheets = len(selected_sheets)
            for idx, sheet_id in enumerate(selected_sheets, 1):
                log_callback(f"Đang copy sheet {idx}/{total_sheets}...")
                sheets_service.spreadsheets().sheets().copyTo(
                    spreadsheetId=source_id,
                    sheetId=sheet_id,
                    body={'destinationSpreadsheetId': new_id}
                ).execute()

            # Rename sheets (remove "Copy of " prefix)
            log_callback("Đang cập nhật tên sheets...")
            spreadsheet = sheets_service.spreadsheets().get(spreadsheetId=new_id).execute()
            rename_requests = []

            for sheet in spreadsheet.get('sheets', []):
                sheet_id = sheet['properties']['sheetId']
                title = sheet['properties'].get('title', '')
                if title.startswith("Copy of "):
                    new_title = title[8:]
                    rename_requests.append({
                        "updateSheetProperties": {
                            "properties": {"sheetId": sheet_id, "title": new_title},
                            "fields": "title"
                        }
                    })

            if rename_requests:
                sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=new_id,
                    body={"requests": rename_requests}
                ).execute()

            # Xóa sheet mặc định nếu có
            log_callback("Đang dọn dẹp...")
            new_spreadsheet_details = sheets_service.spreadsheets().get(spreadsheetId=new_id).execute()
            default_sheet_id = None
            for sheet in new_spreadsheet_details.get('sheets', []):
                if sheet['properties'].get('title', '') in ("Trang tính 1", "Trang tính1", "Sheet1"):
                    default_sheet_id = sheet['properties']['sheetId']
                    break

            if default_sheet_id is not None:
                sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=new_id,
                    body={'requests': [{'deleteSheet': {'sheetId': default_sheet_id}}]}
                ).execute()

            # Force recalculate
            force_recalculate_sheets(sheets_service, new_id)

            # Lưu ID vào temp file
            temp_file_path = os.path.join(TEMP_DIR, f"temp_spreadsheet_id_{task_id}.txt")
            with open(temp_file_path, "w", encoding="utf-8") as f:
                f.write(new_id)

            with task_lock:
                if task_id in tasks:
                    tasks[task_id]['status'] = 'completed'
                    tasks[task_id]['result_id'] = new_id
                    tasks[task_id]['result_url'] = f'https://docs.google.com/spreadsheets/d/{new_id}/edit'
                    log_callback("✅ Tạo spreadsheet thành công!")

        else:  # manual copy
            destination_id = destination_data.get('destination_id')
            log_callback("Đang copy sheets...")
            total_sheets = len(selected_sheets)

            for idx, sheet_id in enumerate(selected_sheets, 1):
                log_callback(f"Đang copy sheet {idx}/{total_sheets}...")
                sheets_service.spreadsheets().sheets().copyTo(
                    spreadsheetId=source_id,
                    sheetId=sheet_id,
                    body={'destinationSpreadsheetId': destination_id}
                ).execute()

            # Rename sheets
            log_callback("Đang cập nhật tên sheets...")
            spreadsheet = sheets_service.spreadsheets().get(spreadsheetId=destination_id).execute()
            rename_requests = []

            for sheet in spreadsheet.get('sheets', []):
                sheet_id = sheet['properties']['sheetId']
                title = sheet['properties'].get('title', '')
                if title.startswith("Copy of "):
                    new_title = title[8:]
                    rename_requests.append({
                        "updateSheetProperties": {
                            "properties": {"sheetId": sheet_id, "title": new_title},
                            "fields": "title"
                        }
                    })

            if rename_requests:
                sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=destination_id,
                    body={"requests": rename_requests}
                ).execute()

            force_recalculate_sheets(sheets_service, destination_id)

            with task_lock:
                if task_id in tasks:
                    tasks[task_id]['status'] = 'completed'
                    tasks[task_id]['result_id'] = destination_id
                    log_callback("✅ Copy sheets thành công!")

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                log_callback(f"❌ Lỗi: {str(e)}")

@app.route('/api/internal_data/copy_sheets', methods=['POST'])
def internal_data_copy_sheets():
    """Copy sheets từ template sang spreadsheet mới hoặc đích"""
    data = request.json
    source_id = data.get('source_id', '')
    selected_sheets = data.get('selected_sheets', [])  # List of sheet IDs
    copy_mode = data.get('copy_mode', 'auto')  # auto hoặc manual
    destination_data = data.get('destination_data', {})

    if not source_id or not selected_sheets:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp source ID và chọn ít nhất một sheet'})

    if copy_mode == 'manual' and not destination_data.get('destination_id'):
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp destination ID cho manual copy'})

    gsheet_manager = get_internal_data_manager()
    if not gsheet_manager:
        return jsonify({'success': False, 'message': 'Google Sheets Manager không khả dụng'})

    # Tạo task ID
    task_id = str(uuid.uuid4())

    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'log_messages': [],
            'type': 'internal_data_copy'
        }

    # Bắt đầu background thread
    thread = threading.Thread(
        target=process_copy_sheets,
        args=(task_id, gsheet_manager, source_id, selected_sheets, copy_mode, destination_data)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': 'Đã bắt đầu copy sheets'
    })

def clean_product_id(id_str):
    """Làm sạch mã sản phẩm: loại bỏ khoảng trắng, dấu nháy và ký tự đặc biệt"""
    cleaned = id_str.strip()
    cleaned = cleaned.replace('"', '').replace("'", "")
    cleaned = cleaned.replace(" ", "")
    return cleaned

@app.route('/api/internal_data/update_formulas', methods=['POST'])
def internal_data_update_formulas():
    """Cập nhật dynamic formulas trong spreadsheet"""
    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    formulas_data = data.get('formulas_data', {})

    if not spreadsheet_id:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp Spreadsheet ID'})

    gsheet_manager = get_internal_data_manager()
    if not gsheet_manager:
        return jsonify({'success': False, 'message': 'Google Sheets Manager không khả dụng'})

    try:
        sheets_service = gsheet_manager.get_sheets_service()

        # Chuẩn bị batch update data
        batch_data = []

        # Cluster devider formulas
        cluster_id = formulas_data.get('cluster_id', '')
        cluster_sheet = formulas_data.get('cluster_sheet', '')
        if cluster_id and cluster_sheet:
            formula_cluster_A3 = f'=IMPORTRANGE("{cluster_id}","{cluster_sheet}!D4:D")'
            formula_cluster_B3 = f'=IMPORTRANGE("{cluster_id}","{cluster_sheet}!H4:H")'
            batch_data.extend([
                {"range": "'Cluster devider'!A3", "values": [[formula_cluster_A3]]},
                {"range": "'Cluster devider'!B3", "values": [[formula_cluster_B3]]}
            ])

        # Source control
        source_id = formulas_data.get('source_id', '')
        source_sheet = formulas_data.get('source_sheet', '')
        if source_id and source_sheet:
            batch_data.extend([
                {"range": "'Source control'!B2", "values": [[source_id]]},
                {"range": "'Source control'!B3", "values": [[source_sheet]]}
            ])

        # Shorten of ext formulas
        shorten_id = formulas_data.get('shorten_id', '')
        shorten_sheet = formulas_data.get('shorten_sheet', '')
        if shorten_id and shorten_sheet:
            formula_shorten_A1 = f'=IMPORTRANGE("{shorten_id}","{shorten_sheet}!D:N")'
            formula_shorten_L1 = f'=IMPORTRANGE("{shorten_id}","{shorten_sheet}!AA:AC")'
            batch_data.extend([
                {"range": "'Shorten of ext'!A1", "values": [[formula_shorten_A1]]},
                {"range": "'Shorten of ext'!L1", "values": [[formula_shorten_L1]]}
            ])

        if batch_data:
            body = {"valueInputOption": "USER_ENTERED", "data": batch_data}
            sheets_service.spreadsheets().values().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()

            return jsonify({'success': True, 'message': 'Cập nhật formulas thành công'})
        else:
            return jsonify({'success': False, 'message': 'Không có dữ liệu để cập nhật'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật formulas: {str(e)}'})

@app.route('/api/internal_data/update_deal_list', methods=['POST'])
def internal_data_update_deal_list():
    """Cập nhật Deal list với product IDs"""
    data = request.json
    spreadsheet_id = data.get('spreadsheet_id', '')
    deal_ids = data.get('deal_ids', [])

    if not spreadsheet_id:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp Spreadsheet ID'})

    if not deal_ids:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp danh sách Product IDs'})

    gsheet_manager = get_internal_data_manager()
    if not gsheet_manager:
        return jsonify({'success': False, 'message': 'Google Sheets Manager không khả dụng'})

    try:
        sheets_service = gsheet_manager.get_sheets_service()

        # Làm sạch và loại bỏ trùng lặp
        cleaned_ids = []
        seen_ids = set()
        for id_val in deal_ids:
            cleaned_id = clean_product_id(str(id_val))
            if cleaned_id and cleaned_id not in seen_ids:
                cleaned_ids.append(cleaned_id)
                seen_ids.add(cleaned_id)

        num_rows = len(cleaned_ids)
        range_B = f"'Deal list'!B4:B{num_rows+3}"
        range_D = f"'Deal list'!D4:D{num_rows+3}"

        # Cập nhật cột B và D với IDs
        id_values = [[id_val] for id_val in cleaned_ids]
        batch_data = {
            'valueInputOption': 'RAW',
            'data': [
                {'range': range_B, 'values': id_values},
                {'range': range_D, 'values': id_values}
            ]
        }

        sheets_service.spreadsheets().values().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body=batch_data
        ).execute()

        # Lấy Deal list sheet ID để copy formulas
        ss = sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        deal_sheet_id = None
        for sheet in ss.get('sheets', []):
            if sheet['properties'].get('title', '') == "Deal list":
                deal_sheet_id = sheet['properties']['sheetId']
                break

        if deal_sheet_id is None:
            return jsonify({'success': False, 'message': 'Không tìm thấy sheet "Deal list"'})

        # Copy formulas từ dòng 4 xuống các dòng mới
        destination_start = 4  # Dòng 5 (index 4)
        destination_end = num_rows + 3
        requests = []

        # Copy công thức cho cột A (index 0)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3, "endRowIndex": 4,
                    "startColumnIndex": 0, "endColumnIndex": 1
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start, "endRowIndex": destination_end,
                    "startColumnIndex": 0, "endColumnIndex": 1
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        # Copy công thức cho cột C (index 2)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3, "endRowIndex": 4,
                    "startColumnIndex": 2, "endColumnIndex": 3
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start, "endRowIndex": destination_end,
                    "startColumnIndex": 2, "endColumnIndex": 3
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        # Copy công thức cho các cột từ E đến BU (index 4 đến 73)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3, "endRowIndex": 4,
                    "startColumnIndex": 4, "endColumnIndex": 73
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start, "endRowIndex": destination_end,
                    "startColumnIndex": 4, "endColumnIndex": 73
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        sheets_service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body={"requests": requests}
        ).execute()

        return jsonify({
            'success': True,
            'message': f'Cập nhật Deal list thành công với {len(cleaned_ids)} Product IDs'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật Deal list: {str(e)}'})

def open_browser():
    """Mở trình duyệt sau khi server đã khởi động"""
    time.sleep(1.5)  # Đợi server khởi động hoàn tất
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    # Khởi động thread để mở trình duyệt
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    # Khởi động Flask server
    print("🚀 Đang khởi động server...")
    print("🌐 Server sẽ chạy tại: http://127.0.0.1:5000")
    print("🔗 Trình duyệt sẽ tự động mở trong giây lát...")
    app.run(debug=True, use_reloader=False)